@php
    $tagline = $attributes['tagline'] ?? '';
    $title = $attributes['title'] ?? '';
    $description = $attributes['description'] ?? '';
    $benefits = $attributes['benefits'] ?? [];
    $buttonText = $attributes['buttonText'] ?? '';
    $buttonLink = $attributes['buttonLink'] ?? '#';
@endphp

<div class="large-cta-section relative flex justify-center items-center mb-8">
    <div class="container lg:max-w-[1200px] lg:m-auto">
        <div class="w-full p-12 flex flex-col justify-between items-start relative overflow-hidden lg:rounded-3xl" style="background: linear-gradient(to bottom right, #daeffb, #acdbf2);">
            {{-- Noise texture overlay --}}
            <div class="absolute inset-0 z-0 opacity-20 bg-repeat" style="background-image: url('{{ asset('images/noise.svg') }}');"></div>

            <div class="relative z-10 w-full">
                <div class="text-center mb-12">
                    @if(!empty($tagline))
                        <h5 class="text-sm font-semibold text-[#3F4A5A] uppercase tracking-wider mb-4">{!! $tagline !!}</h5>
                    @endif

                    @if(!empty($title))
                        <x-title variation="large" class="mb-6">{!! $title !!}</x-title>
                    @endif

                    @if(!empty($description))
                        <p class="text-bodyLarge text-[#3F4A5A] font-normal lg:text-bodyLargeDesktop max-w-3xl mx-auto mb-8">{!! $description !!}</p>
                    @endif
                </div>

                @if(!empty($benefits) && count($benefits) > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
                        @foreach($benefits as $benefit)
                            @if(!empty($benefit['text']))
                                <div class="bg-white/20 backdrop-blur-sm rounded-2xl p-6 border border-white/30">
                                    <div class="flex items-start gap-3">
                                        <div class="flex-shrink-0 w-6 h-6 bg-white rounded-full flex items-center justify-center mt-1">
                                            <svg class="w-3 h-3 text-[#3F4A5A]" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <p class="text-[#3F4A5A] font-medium text-base leading-relaxed">{!! $benefit['text'] !!}</p>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                @endif

                @if(!empty($buttonText) && !empty($buttonLink))
                    <div class="text-center">
                        <x-button
                            type="link"
                            :href="$buttonLink"
                            target="_blank"
                            rel="noopener noreferrer"
                            size="large"
                            variant="primary">
                            {{ $buttonText }}
                        </x-button>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
